# Term definition

This document defines the terms used in this documentation.

 - Component - BacPack system component
 - Project - CMake based project
 - Package - a set of files which is an output of CMake build; the definition of Package can
 have any number of dependencies
 - App - same as Package, except it cannot have any dependencies in its definition
 - Config - Package definition as a JSON file
