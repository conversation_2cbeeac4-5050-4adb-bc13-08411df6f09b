# BacPack System Documentation

This repository contains the official documentation for the BacPack System, a component stack
developed by [BringAuto](https://bringauto.com/) for easy dependency and package management of
CMake-based projects.

The documentation is built with MkDocs and hosted at: **https://bacpack-system.github.io**

## Documentation Structure

- **[Introduction](docs/index.md)** - Overview of the BacPack System
- **[System Architecture](docs/architecture.md)** - Detailed Component descriptions and relationships
- **[Use Cases](docs/use_cases.md)** - Common use case scenarios
- **[Example Usage](docs/example_usage.md)** - Step-by-step usage guide
- **[Term Definition](docs/term_definition.md)** - Glossary of terms used in the documentation

## Local Development

To run the documentation locally:

```bash
# Install MkDocs with Material theme
pip install mkdocs mkdocs-material

# Serve the documentation
mkdocs serve
```

The documentation will be available at `http://localhost:8000`
