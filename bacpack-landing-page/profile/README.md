# BacPack System

BacPack is a component stack designed to create an environment for easy dependency and package management of CMake-based projects.

## What is Ba<PERSON><PERSON><PERSON>?

BacPack provides a complete solution for building, storing, and distributing C++ dependencies across multiple platforms using Docker containers and Git LFS storage.

**Key Features:**
- 🐳 **Cross-platform builds** using Docker containers
- 📦 **Package management** with dependency resolution
- 🔄 **Automated builds** with topological dependency sorting
- 📚 **Git LFS storage** for efficient package distribution
- ⚙️ **CMake integration** for seamless project setup

## Main Components

- **[Packager](https://github.com/bacpack-system/packager)** - CLI tool that builds and stores Packages using Docker containers and Git LFS
- **[Package Tracker](https://github.com/bacpack-system/package-tracker)** - CMake integration tool that downloads dependencies using the `BA_PACKAGE_LIBRARY()` macro

## Get Started

📖 **[Read the Documentation](https://bacpack-system.github.io/)** - Complete guide with examples and tutorials

🚀 **[Example Usage](https://bacpack-system.github.io/example_usage/)** - Step-by-step tutorial for building your first Package

## Example Projects

- **[Example Context](https://github.com/bacpack-system/example-context)** - Sample Package definitions for curl and zlib
- **[Example Project](https://github.com/bacpack-system/example-project)** - CMake project demonstrating BacPack usage

---

*BacPack System - Simplifying C++ dependency management*
