# Install script for directory: /home/<USER>/work/docs/example-project

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set path to fallback-tool for dependency-resolution.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg]|[Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd" AND
       NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd")
      file(RPATH_CHECK
           FILE "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd"
           RPATH "")
    endif()
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "/home/<USER>/work/docs/example-project/_b/example-projectd")
    if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd" AND
       NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd")
      file(RPATH_CHANGE
           FILE "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd"
           OLD_RPATH "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib:/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib:"
           NEW_RPATH "")
      if(CMAKE_INSTALL_DO_STRIP)
        execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/example-projectd")
      endif()
    endif()
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg]|[Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/example-project.cmake")
      file(DIFFERENT _cmake_export_file_changed FILES
           "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/example-project.cmake"
           "/home/<USER>/work/docs/example-project/_b/CMakeFiles/Export/c220ae0af1591e9e9e916bba91f25986/example-project.cmake")
      if(_cmake_export_file_changed)
        file(GLOB _cmake_old_config_files "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/example-project-*.cmake")
        if(_cmake_old_config_files)
          string(REPLACE ";" ", " _cmake_old_config_files_text "${_cmake_old_config_files}")
          message(STATUS "Old export file \"$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/example-project.cmake\" will be replaced.  Removing files [${_cmake_old_config_files_text}].")
          unset(_cmake_old_config_files_text)
          file(REMOVE ${_cmake_old_config_files})
        endif()
        unset(_cmake_old_config_files)
      endif()
      unset(_cmake_export_file_changed)
    endif()
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake" TYPE FILE FILES "/home/<USER>/work/docs/example-project/_b/CMakeFiles/Export/c220ae0af1591e9e9e916bba91f25986/example-project.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake" TYPE FILE FILES "/home/<USER>/work/docs/example-project/_b/CMakeFiles/Export/c220ae0af1591e9e9e916bba91f25986/example-project-debug.cmake")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE FILE FILES "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/libcurl-d.so")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE FILE FILES "/usr/lib/x86_64-linux-gnu/libssl.so.3")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library_name libssl.so.3)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_link_name    libssl.so)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir  lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  		SET(destdir "$ENV{DESTDIR}")
		SET(working_directory)
		IF(destdir)
			SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}/${_ba_package_deps_install_dir}")
		ELSE()
			SET(working_directory "${CMAKE_INSTALL_PREFIX}/${_ba_package_deps_install_dir}")
		ENDIF()
		EXECUTE_PROCESS(
			COMMAND ${CMAKE_COMMAND} -E create_symlink ${_ba_package_deps_library_name} ${_ba_package_deps_link_name}
			RESULT_VARIABLE    result
			WORKING_DIRECTORY "${working_directory}"
		)
		IF(NOT result EQUAL 0)
			MESSAGE(FATAL_ERROR "Cannot create symlink ${_ba_package_deps_link_name}")
		ENDIF()
	
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE FILE FILES "/usr/lib/x86_64-linux-gnu/libcrypto.so.3")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library_name libcrypto.so.3)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_link_name    libcrypto.so)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir  lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  		SET(destdir "$ENV{DESTDIR}")
		SET(working_directory)
		IF(destdir)
			SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}/${_ba_package_deps_install_dir}")
		ELSE()
			SET(working_directory "${CMAKE_INSTALL_PREFIX}/${_ba_package_deps_install_dir}")
		ENDIF()
		EXECUTE_PROCESS(
			COMMAND ${CMAKE_COMMAND} -E create_symlink ${_ba_package_deps_library_name} ${_ba_package_deps_link_name}
			RESULT_VARIABLE    result
			WORKING_DIRECTORY "${working_directory}"
		)
		IF(NOT result EQUAL 0)
			MESSAGE(FATAL_ERROR "Cannot create symlink ${_ba_package_deps_link_name}")
		ENDIF()
	
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE FILE FILES "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE FILE FILES "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so.1")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE FILE FILES "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so.1.2.11")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libcurl-d.so)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libssl.so.3)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libssl.so)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libcrypto.so.3)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libcrypto.so)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libz.so)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libz.so.1)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_library     libz.so.1.2.11)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  SET(_ba_package_deps_install_dir lib)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  			FIND_PROGRAM(patchelf patchelf REQUIRED)
			MESSAGE(STATUS "patchelf update R/RUNPATH: ${_ba_package_deps_install_dir}/${_ba_package_deps_library}")
			SET(destdir "$ENV{DESTDIR}")
			SET(working_directory)
			IF(destdir)
				SET(working_directory "${destdir}/${CMAKE_INSTALL_PREFIX}")
			ELSE()
				SET(working_directory "${CMAKE_INSTALL_PREFIX}")
			ENDIF()
			EXECUTE_PROCESS(
				COMMAND           ${patchelf} --set-rpath $ORIGIN ${_ba_package_deps_install_dir}/${_ba_package_deps_library}
				RESULT_VARIABLE    result
				WORKING_DIRECTORY "${working_directory}"
			)
			IF(NOT result EQUAL 0)
				MESSAGE(FATAL_ERROR "Cannot update R/RUNPATH for ${install_dir}/${library}")
			ENDIF()
		
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
if(CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "/home/<USER>/work/docs/example-project/_b/install_local_manifest.txt"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
if(CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_COMPONENT MATCHES "^[a-zA-Z0-9_.+-]+$")
    set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
  else()
    string(MD5 CMAKE_INST_COMP_HASH "${CMAKE_INSTALL_COMPONENT}")
    set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INST_COMP_HASH}.txt")
    unset(CMAKE_INST_COMP_HASH)
  endif()
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "/home/<USER>/work/docs/example-project/_b/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
