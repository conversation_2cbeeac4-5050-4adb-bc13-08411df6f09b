# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1479/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1479/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/docs/example-project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/docs/example-project/_b

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/example-project.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/example-project.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/example-project.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/example-project.dir

# All Build rule for target.
CMakeFiles/example-project.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example-project.dir/build.make CMakeFiles/example-project.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example-project.dir/build.make CMakeFiles/example-project.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/docs/example-project/_b/CMakeFiles --progress-num=1,2 "Built target example-project"
.PHONY : CMakeFiles/example-project.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/example-project.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/docs/example-project/_b/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/example-project.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/docs/example-project/_b/CMakeFiles 0
.PHONY : CMakeFiles/example-project.dir/rule

# Convenience name for target.
example-project: CMakeFiles/example-project.dir/rule
.PHONY : example-project

# codegen rule for target.
CMakeFiles/example-project.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example-project.dir/build.make CMakeFiles/example-project.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/docs/example-project/_b/CMakeFiles --progress-num=1,2 "Finished codegen for target example-project"
.PHONY : CMakeFiles/example-project.dir/codegen

# clean rule for target.
CMakeFiles/example-project.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example-project.dir/build.make CMakeFiles/example-project.dir/clean
.PHONY : CMakeFiles/example-project.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

