
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/home/<USER>/.local/bin/uname"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/uname"
      - "/home/<USER>/.pyenv/shims/uname"
      - "/home/<USER>/.pyenv/bin/uname"
      - "/usr/local/sbin/uname"
      - "/usr/local/bin/uname"
      - "/usr/sbin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:6 (PROJECT)"
    message: |
      The system is: Linux - 6.8.0-71-generic - x86_64
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/home/<USER>/.local/bin/gmake"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/gmake"
      - "/home/<USER>/.pyenv/shims/gmake"
      - "/home/<USER>/.pyenv/bin/gmake"
      - "/usr/local/sbin/gmake"
      - "/usr/local/bin/gmake"
      - "/usr/sbin/gmake"
    found: "/usr/bin/gmake"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "CC"
      - "g++"
      - "aCC"
      - "cl"
      - "bcc"
      - "xlC"
      - "icpx"
      - "icx"
      - "clang++"
    candidate_directories:
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/home/<USER>/.local/bin/c++"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/c++"
      - "/home/<USER>/.pyenv/shims/c++"
      - "/home/<USER>/.pyenv/bin/c++"
      - "/usr/local/sbin/c++"
      - "/usr/local/bin/c++"
      - "/usr/sbin/c++"
    found: "/usr/bin/c++"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/"
    found: "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "message-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (PROJECT)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/work/docs/example-project/_b/CMakeFiles/4.1.0/CompilerIdCXX/a.out
      
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/ar"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/ranlib"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/nm"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/objcopy"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/readelf"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/usr/bin/dlltool"
      - "/home/<USER>/.local/bin/dlltool"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/dlltool"
      - "/home/<USER>/.pyenv/shims/dlltool"
      - "/home/<USER>/.pyenv/bin/dlltool"
      - "/usr/local/sbin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/usr/sbin/dlltool"
      - "/sbin/dlltool"
      - "/bin/dlltool"
      - "/usr/games/dlltool"
      - "/usr/local/games/dlltool"
      - "/snap/bin/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    found: "/usr/bin/addr2line"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/usr/bin/tapi"
      - "/home/<USER>/.local/bin/tapi"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/tapi"
      - "/home/<USER>/.pyenv/shims/tapi"
      - "/home/<USER>/.pyenv/bin/tapi"
      - "/usr/local/sbin/tapi"
      - "/usr/local/bin/tapi"
      - "/usr/sbin/tapi"
      - "/sbin/tapi"
      - "/bin/tapi"
      - "/usr/games/tapi"
      - "/usr/local/games/tapi"
      - "/snap/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-13.3"
      - "gcc-ar-13"
      - "gcc-ar13"
      - "gcc-ar"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/usr/bin/gcc-ar-13.3"
      - "/home/<USER>/.local/bin/gcc-ar-13.3"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/gcc-ar-13.3"
      - "/home/<USER>/.pyenv/shims/gcc-ar-13.3"
      - "/home/<USER>/.pyenv/bin/gcc-ar-13.3"
      - "/usr/local/sbin/gcc-ar-13.3"
      - "/usr/local/bin/gcc-ar-13.3"
      - "/usr/sbin/gcc-ar-13.3"
      - "/sbin/gcc-ar-13.3"
      - "/bin/gcc-ar-13.3"
      - "/usr/games/gcc-ar-13.3"
      - "/usr/local/games/gcc-ar-13.3"
      - "/snap/bin/gcc-ar-13.3"
    found: "/usr/bin/gcc-ar-13"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:6 (PROJECT)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-13.3"
      - "gcc-ranlib-13"
      - "gcc-ranlib13"
      - "gcc-ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
    searched_directories:
      - "/usr/bin/gcc-ranlib-13.3"
      - "/home/<USER>/.local/bin/gcc-ranlib-13.3"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/gcc-ranlib-13.3"
      - "/home/<USER>/.pyenv/shims/gcc-ranlib-13.3"
      - "/home/<USER>/.pyenv/bin/gcc-ranlib-13.3"
      - "/usr/local/sbin/gcc-ranlib-13.3"
      - "/usr/local/bin/gcc-ranlib-13.3"
      - "/usr/sbin/gcc-ranlib-13.3"
      - "/sbin/gcc-ranlib-13.3"
      - "/bin/gcc-ranlib-13.3"
      - "/usr/games/gcc-ranlib-13.3"
      - "/usr/local/games/gcc-ranlib-13.3"
      - "/snap/bin/gcc-ranlib-13.3"
    found: "/usr/bin/gcc-ranlib-13"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (PROJECT)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx"
      binary: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx'
        
        Run Build Command(s): /snap/cmake/1479/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_9da73/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_9da73.dir/build.make CMakeFiles/cmTC_9da73.dir/build
        gmake[1]: Entering directory '/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx'
        Building CXX object CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -c /snap/cmake/1479/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9da73.dir/'
         /usr/libexec/gcc/x86_64-linux-gnu/13/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /snap/cmake/1479/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_9da73.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccYPmJJz.s
        GNU C++17 (Ubuntu 13.3.0-6ubuntu2~24.04) version 13.3.0 (x86_64-linux-gnu)
        	compiled by GNU C version 13.3.0, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.26-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/13"
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/13/include-fixed/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/13/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/include/c++/13
         /usr/include/x86_64-linux-gnu/c++/13
         /usr/include/c++/13/backward
         /usr/lib/gcc/x86_64-linux-gnu/13/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        Compiler executable checksum: c81c05345ce537099dafd5580045814a
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9da73.dir/'
         as -v --64 -o CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccYPmJJz.s
        GNU assembler version 2.42 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.42
        COMPILER_PATH=/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_9da73
        /snap/cmake/1479/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9da73.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
        COMPILER_PATH=/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_9da73' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_9da73.'
         /usr/libexec/gcc/x86_64-linux-gnu/13/collect2 -plugin /usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper -plugin-opt=-fresolution=/tmp/ccXYq04q.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_9da73 /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -v CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o
        collect2 version 13.3.0
        /usr/bin/ld -plugin /usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper -plugin-opt=-fresolution=/tmp/ccXYq04q.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_9da73 /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -v CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o
        GNU ld (GNU Binutils for Ubuntu) 2.42
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_9da73' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_9da73.'
        /usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_9da73
        gmake[1]: Leaving directory '/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (PROJECT)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/include/c++/13]
          add: [/usr/include/x86_64-linux-gnu/c++/13]
          add: [/usr/include/c++/13/backward]
          add: [/usr/lib/gcc/x86_64-linux-gnu/13/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/include/c++/13] ==> [/usr/include/c++/13]
        collapse include dir [/usr/include/x86_64-linux-gnu/c++/13] ==> [/usr/include/x86_64-linux-gnu/c++/13]
        collapse include dir [/usr/include/c++/13/backward] ==> [/usr/include/c++/13/backward]
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/13/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/13;/usr/include/x86_64-linux-gnu/c++/13;/usr/include/c++/13/backward;/usr/lib/gcc/x86_64-linux-gnu/13/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (PROJECT)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx']
        ignore line: []
        ignore line: [Run Build Command(s): /snap/cmake/1479/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_9da73/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_9da73.dir/build.make CMakeFiles/cmTC_9da73.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/work/docs/example-project/_b/CMakeFiles/CMakeScratch/TryCompile-dGZmrx']
        ignore line: [Building CXX object CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -c /snap/cmake/1479/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c ada c++ go d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9da73.dir/']
        ignore line: [ /usr/libexec/gcc/x86_64-linux-gnu/13/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /snap/cmake/1479/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_9da73.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccYPmJJz.s]
        ignore line: [GNU C++17 (Ubuntu 13.3.0-6ubuntu2~24.04) version 13.3.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 13.3.0  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.26-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/13"]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/13/include-fixed/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/13/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/include/c++/13]
        ignore line: [ /usr/include/x86_64-linux-gnu/c++/13]
        ignore line: [ /usr/include/c++/13/backward]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/13/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: c81c05345ce537099dafd5580045814a]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9da73.dir/']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccYPmJJz.s]
        ignore line: [GNU assembler version 2.42 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.42]
        ignore line: [COMPILER_PATH=/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_9da73]
        ignore line: [/snap/cmake/1479/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9da73.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c ada c++ go d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) ]
        ignore line: [COMPILER_PATH=/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/13/:/usr/libexec/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/13/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/13/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_9da73' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_9da73.']
        link line: [ /usr/libexec/gcc/x86_64-linux-gnu/13/collect2 -plugin /usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper -plugin-opt=-fresolution=/tmp/ccXYq04q.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_9da73 /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -v CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/libexec/gcc/x86_64-linux-gnu/13/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccXYq04q.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_9da73] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/13] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/13/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o]
        ignore line: [collect2 version 13.3.0]
        ignore line: [/usr/bin/ld -plugin /usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper -plugin-opt=-fresolution=/tmp/ccXYq04q.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_9da73 /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. -v CMakeFiles/cmTC_9da73.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13] ==> [/usr/lib/gcc/x86_64-linux-gnu/13]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/13/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/13;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (PROJECT)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils for Ubuntu) 2.42
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindGit.cmake:86 (find_program)"
      - "/home/<USER>/work/cmakelib/system_modules/CMLIB_REQUIRED_ENV.cmake:43 (FIND_PACKAGE)"
      - "/home/<USER>/work/cmakelib/system_modules/CMLIB_REQUIRED_ENV.cmake:26 (_CMLIB_REQUIRED_ENV_FIND_GIT)"
      - "/home/<USER>/work/cmakelib/system_modules/CMLIB_REQUIRED_ENV.cmake:127 (CMLIB_REQUIRED_ENV)"
      - "/home/<USER>/work/cmakelib/CMLIB.cmake:80 (INCLUDE)"
      - "/home/<USER>/work/cmakelib/CMLIB.cmake:94 (_CMLIB_LIBRARY_MANAGER)"
      - "/home/<USER>/work/cmakelib/CMLIBConfig.cmake:8 (INCLUDE)"
      - "CMakeLists.txt:12 (FIND_PACKAGE)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Git command line client"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
    candidate_directories:
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/snap/cmake/1479/bin/"
      - "/snap/cmake/1479/sbin/"
      - "/snap/cmake/1479/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
    searched_directories:
      - "/home/<USER>/.local/bin/git"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/git"
      - "/home/<USER>/.pyenv/shims/git"
      - "/home/<USER>/.pyenv/bin/git"
      - "/usr/local/sbin/git"
      - "/usr/local/bin/git"
      - "/usr/sbin/git"
    found: "/usr/bin/git"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/home/<USER>/work/cmakelib/system_modules/CMLIB_COMPONENT.cmake:91 (FIND_PACKAGE)"
      - "/home/<USER>/work/cmakelib/CMLIB.cmake:102 (CMLIB_COMPONENT)"
      - "/home/<USER>/work/cmakelib/CMLIBConfig.cmake:8 (INCLUDE)"
      - "CMakeLists.txt:12 (FIND_PACKAGE)"
    name: "STORAGE"
    configs:
      -
        filename: "STORAGEConfig.cmake"
        kind: "cmake"
      -
        filename: "storage-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "optional"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "STORAGE"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.local/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.local/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.pyenv/shims/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.pyenv/shims/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.pyenv/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/.pyenv/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/cmake/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/cmake/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/games/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/games/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/games/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/usr/local/games/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/snap/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/snap/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/snap/cmake/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/snap/cmake/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/snap/cmake/1479/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/snap/cmake/1479/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/STORAGEConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/storage-config.cmake"
        mode: "config"
        reason: "no_exist"
    found: null
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_MODULE_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMDEF"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/STORAGE"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMUTIL"
  -
    kind: "find_package-v1"
    backtrace:
      - "CMakeLists.txt:12 (FIND_PACKAGE)"
    name: "CMLIB"
    components:
      -
        name: "CMDEF"
        required: true
        found: false
      -
        name: "STORAGE"
        required: true
        found: true
    configs:
      -
        filename: "CMLIBConfig.cmake"
        kind: "cmake"
      -
        filename: "cmlib-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: false
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "CMLIB"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects/CMLIBConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects/cmlib-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/home/<USER>/work/cmakelib/CMLIBConfig.cmake"
      mode: "config"
      version: ""
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_MODULE_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMDEF"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/STORAGE"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMUTIL"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMCONF"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindZLIB.cmake:152 (find_path)"
      - "CMakeLists.txt:25 (FIND_PACKAGE)"
    mode: "path"
    variable: "ZLIB_INCLUDE_DIR"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "zlib.h"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
    found: "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/"
    search_context:
      package_stack:
        -
          package_paths:
            - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_INCLUDE_PATH:
        - "/usr/include/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindZLIB.cmake:182 (find_library)"
      - "CMakeLists.txt:25 (FIND_PACKAGE)"
    mode: "library"
    variable: "ZLIB_LIBRARY_RELEASE"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "z"
      - "zlib"
      - "zdll"
      - "zlib1"
      - "zlibstatic"
      - "zlibwapi"
      - "zlibvc"
      - "zlibstat"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
    found: "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so"
    search_context:
      package_stack:
        -
          package_paths:
            - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindZLIB.cmake:183 (find_library)"
      - "CMakeLists.txt:25 (FIND_PACKAGE)"
    mode: "library"
    variable: "ZLIB_LIBRARY_DEBUG"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "zd"
      - "zlibd"
      - "zdlld"
      - "zlibd1"
      - "zlib1d"
      - "zlibstaticd"
      - "zlibwapid"
      - "zlibvcd"
      - "zlibstatd"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
    searched_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
    found: false
    search_context:
      package_stack:
        -
          package_paths:
            - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:69 (find_program)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindOpenSSL.cmake:261 (find_package)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake:50 (find_dependency)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    mode: "program"
    variable: "PKG_CONFIG_EXECUTABLE"
    description: "pkg-config executable"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pkg-config"
      - "pkgconf"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/bin/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/sbin/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/bin/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/sbin/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/snap/cmake/1479/bin/"
      - "/snap/cmake/1479/sbin/"
      - "/snap/cmake/1479/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
    searched_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/bin/pkg-config"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/bin/pkgconf"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/sbin/pkg-config"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/sbin/pkgconf"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/pkg-config"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/pkgconf"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/bin/pkg-config"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/bin/pkgconf"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/sbin/pkg-config"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/sbin/pkgconf"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/pkg-config"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/pkgconf"
      - "/home/<USER>/.local/bin/pkg-config"
      - "/home/<USER>/.local/bin/pkgconf"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/pkg-config"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/pkgconf"
      - "/home/<USER>/.pyenv/shims/pkg-config"
      - "/home/<USER>/.pyenv/shims/pkgconf"
      - "/home/<USER>/.pyenv/bin/pkg-config"
      - "/home/<USER>/.pyenv/bin/pkgconf"
      - "/usr/local/sbin/pkg-config"
      - "/usr/local/sbin/pkgconf"
      - "/usr/local/bin/pkg-config"
      - "/usr/local/bin/pkgconf"
      - "/usr/sbin/pkg-config"
      - "/usr/sbin/pkgconf"
    found: "/usr/bin/pkg-config"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindOpenSSL.cmake:263 (pkg_check_modules)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake:50 (find_dependency)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    mode: "library"
    variable: "pkgcfg_lib__OPENSSL_ssl"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ssl"
    candidate_directories:
      - "/usr/lib/x86_64-linux-gnu/"
    found: "/usr/lib/x86_64-linux-gnu/libssl.so"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindOpenSSL.cmake:263 (pkg_check_modules)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake:50 (find_dependency)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    mode: "library"
    variable: "pkgcfg_lib__OPENSSL_crypto"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "crypto"
    candidate_directories:
      - "/usr/lib/x86_64-linux-gnu/"
    found: "/usr/lib/x86_64-linux-gnu/libcrypto.so"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindOpenSSL.cmake:336 (find_path)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake:50 (find_dependency)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    mode: "path"
    variable: "OPENSSL_INCLUDE_DIR"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "openssl/ssl.h"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/x86_64-linux-gnu/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/x86_64-linux-gnu/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/usr/include/include/"
      - "/usr/include/"
      - "/home/<USER>/.local/bin/include/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/include/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/include/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/include/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/include/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/include/"
      - "/usr/local/bin/"
      - "/usr/sbin/include/"
      - "/usr/sbin/"
      - "/usr/bin/include/"
      - "/usr/bin/"
      - "/sbin/include/"
      - "/sbin/"
      - "/bin/include/"
      - "/bin/"
      - "/usr/games/include/"
      - "/usr/games/"
      - "/usr/local/games/include/"
      - "/usr/local/games/"
      - "/snap/bin/include/"
      - "/snap/bin/"
      - "/usr/local/include/x86_64-linux-gnu/include/"
      - "/usr/local/include/x86_64-linux-gnu/"
      - "/usr/local/include/include/"
      - "/usr/local/include/"
      - "/usr/local/include/"
      - "/usr/local/"
      - "/usr/include/x86_64-linux-gnu/include/"
      - "/usr/include/x86_64-linux-gnu/"
      - "/usr/include/include/"
      - "/usr/include/"
      - "/usr/include/"
      - "/usr/"
      - "/include/x86_64-linux-gnu/include/"
      - "/include/x86_64-linux-gnu/"
      - "/include/include/"
      - "/include/"
      - "/snap/cmake/1479/include/x86_64-linux-gnu/include/"
      - "/snap/cmake/1479/include/x86_64-linux-gnu/"
      - "/snap/cmake/1479/include/include/"
      - "/snap/cmake/1479/include/"
      - "/snap/cmake/1479/include/"
      - "/snap/cmake/1479/"
      - "/usr/X11R6/include/x86_64-linux-gnu/include/"
      - "/usr/X11R6/include/x86_64-linux-gnu/"
      - "/usr/X11R6/include/include/"
      - "/usr/X11R6/include/"
      - "/usr/X11R6/include/"
      - "/usr/X11R6/"
      - "/usr/pkg/include/x86_64-linux-gnu/include/"
      - "/usr/pkg/include/x86_64-linux-gnu/"
      - "/usr/pkg/include/include/"
      - "/usr/pkg/include/"
      - "/usr/pkg/include/"
      - "/usr/pkg/"
      - "/opt/include/x86_64-linux-gnu/include/"
      - "/opt/include/x86_64-linux-gnu/"
      - "/opt/include/include/"
      - "/opt/include/"
      - "/opt/include/"
      - "/opt/"
      - "/usr/include/X11/include/"
      - "/usr/include/X11/"
    searched_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/x86_64-linux-gnu/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/x86_64-linux-gnu/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/x86_64-linux-gnu/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/x86_64-linux-gnu/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include/openssl/ssl.h"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/openssl/ssl.h"
      - "/usr/include/include/openssl/ssl.h"
    found: "/usr/include/"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_INCLUDE_PATH:
        - "/usr/include/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindOpenSSL.cmake:594 (find_library)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake:50 (find_dependency)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    mode: "library"
    variable: "OPENSSL_SSL_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ssl"
      - "ssleay32"
      - "ssleay32MD"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
      - "/usr/lib/x86_64-linux-gnu/"
      - "/home/<USER>/.local/bin/lib/"
      - "/home/<USER>/.local/bin/lib64/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/lib/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/lib64/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/lib/"
      - "/home/<USER>/.pyenv/shims/lib64/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/lib/"
      - "/home/<USER>/.pyenv/bin/lib64/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/lib/"
      - "/usr/local/sbin/lib64/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/lib/"
      - "/usr/local/bin/lib64/"
      - "/usr/local/bin/"
      - "/usr/sbin/lib/"
      - "/usr/sbin/lib64/"
      - "/usr/sbin/"
      - "/usr/bin/lib/"
      - "/usr/bin/lib64/"
      - "/usr/bin/"
      - "/sbin/lib/"
      - "/sbin/lib64/"
      - "/sbin/"
      - "/bin/lib/"
      - "/bin/lib64/"
      - "/bin/"
      - "/usr/games/lib/"
      - "/usr/games/lib64/"
      - "/usr/games/"
      - "/usr/local/games/lib/"
      - "/usr/local/games/lib64/"
      - "/usr/local/games/"
      - "/snap/bin/lib/"
      - "/snap/bin/lib64/"
      - "/snap/bin/"
      - "/usr/local/lib/x86_64-linux-gnu/lib/"
      - "/usr/local/lib/x86_64-linux-gnu/lib64/"
      - "/usr/local/lib/x86_64-linux-gnu/"
      - "/usr/local/lib/lib/"
      - "/usr/local/lib/lib64/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/lib64/"
      - "/usr/local/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
      - "/usr/lib/x86_64-linux-gnu/"
      - "/usr/lib/lib/"
      - "/usr/lib/lib64/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/lib64/"
      - "/usr/"
      - "/lib/x86_64-linux-gnu/lib/"
      - "/lib/x86_64-linux-gnu/lib64/"
      - "/lib/x86_64-linux-gnu/"
      - "/lib/lib/"
      - "/lib/lib64/"
      - "/lib/"
      - "/snap/cmake/1479/lib/x86_64-linux-gnu/lib/"
      - "/snap/cmake/1479/lib/x86_64-linux-gnu/lib64/"
      - "/snap/cmake/1479/lib/x86_64-linux-gnu/"
      - "/snap/cmake/1479/lib/lib/"
      - "/snap/cmake/1479/lib/lib64/"
      - "/snap/cmake/1479/lib/"
      - "/snap/cmake/1479/lib/"
      - "/snap/cmake/1479/lib64/"
      - "/snap/cmake/1479/"
      - "/usr/X11R6/lib/x86_64-linux-gnu/lib/"
      - "/usr/X11R6/lib/x86_64-linux-gnu/lib64/"
      - "/usr/X11R6/lib/x86_64-linux-gnu/"
      - "/usr/X11R6/lib/lib/"
      - "/usr/X11R6/lib/lib64/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib64/"
      - "/usr/X11R6/"
      - "/usr/pkg/lib/x86_64-linux-gnu/lib/"
      - "/usr/pkg/lib/x86_64-linux-gnu/lib64/"
      - "/usr/pkg/lib/x86_64-linux-gnu/"
      - "/usr/pkg/lib/lib/"
      - "/usr/pkg/lib/lib64/"
      - "/usr/pkg/lib/"
      - "/usr/pkg/lib/"
      - "/usr/pkg/lib64/"
      - "/usr/pkg/"
      - "/opt/lib/x86_64-linux-gnu/lib/"
      - "/opt/lib/x86_64-linux-gnu/lib64/"
      - "/opt/lib/x86_64-linux-gnu/"
      - "/opt/lib/lib/"
      - "/opt/lib/lib64/"
      - "/opt/lib/"
      - "/opt/lib/"
      - "/opt/lib64/"
      - "/opt/"
      - "/usr/lib/X11/lib/"
      - "/usr/lib/X11/lib64/"
      - "/usr/lib/X11/"
    searched_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
    found: "/usr/lib/x86_64-linux-gnu/libssl.so"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindOpenSSL.cmake:608 (find_library)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake:50 (find_dependency)"
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    mode: "library"
    variable: "OPENSSL_CRYPTO_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "crypto"
    candidate_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
      - "/usr/lib/x86_64-linux-gnu/"
      - "/home/<USER>/.local/bin/lib/"
      - "/home/<USER>/.local/bin/lib64/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/lib/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/lib64/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/lib/"
      - "/home/<USER>/.pyenv/shims/lib64/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/lib/"
      - "/home/<USER>/.pyenv/bin/lib64/"
      - "/home/<USER>/.pyenv/bin/"
      - "/usr/local/sbin/lib/"
      - "/usr/local/sbin/lib64/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/lib/"
      - "/usr/local/bin/lib64/"
      - "/usr/local/bin/"
      - "/usr/sbin/lib/"
      - "/usr/sbin/lib64/"
      - "/usr/sbin/"
      - "/usr/bin/lib/"
      - "/usr/bin/lib64/"
      - "/usr/bin/"
      - "/sbin/lib/"
      - "/sbin/lib64/"
      - "/sbin/"
      - "/bin/lib/"
      - "/bin/lib64/"
      - "/bin/"
      - "/usr/games/lib/"
      - "/usr/games/lib64/"
      - "/usr/games/"
      - "/usr/local/games/lib/"
      - "/usr/local/games/lib64/"
      - "/usr/local/games/"
      - "/snap/bin/lib/"
      - "/snap/bin/lib64/"
      - "/snap/bin/"
      - "/usr/local/lib/x86_64-linux-gnu/lib/"
      - "/usr/local/lib/x86_64-linux-gnu/lib64/"
      - "/usr/local/lib/x86_64-linux-gnu/"
      - "/usr/local/lib/lib/"
      - "/usr/local/lib/lib64/"
      - "/usr/local/lib/"
      - "/usr/local/lib/"
      - "/usr/local/lib64/"
      - "/usr/local/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
      - "/usr/lib/x86_64-linux-gnu/"
      - "/usr/lib/lib/"
      - "/usr/lib/lib64/"
      - "/usr/lib/"
      - "/usr/lib/"
      - "/usr/lib64/"
      - "/usr/"
      - "/lib/x86_64-linux-gnu/lib/"
      - "/lib/x86_64-linux-gnu/lib64/"
      - "/lib/x86_64-linux-gnu/"
      - "/lib/lib/"
      - "/lib/lib64/"
      - "/lib/"
      - "/snap/cmake/1479/lib/x86_64-linux-gnu/lib/"
      - "/snap/cmake/1479/lib/x86_64-linux-gnu/lib64/"
      - "/snap/cmake/1479/lib/x86_64-linux-gnu/"
      - "/snap/cmake/1479/lib/lib/"
      - "/snap/cmake/1479/lib/lib64/"
      - "/snap/cmake/1479/lib/"
      - "/snap/cmake/1479/lib/"
      - "/snap/cmake/1479/lib64/"
      - "/snap/cmake/1479/"
      - "/usr/X11R6/lib/x86_64-linux-gnu/lib/"
      - "/usr/X11R6/lib/x86_64-linux-gnu/lib64/"
      - "/usr/X11R6/lib/x86_64-linux-gnu/"
      - "/usr/X11R6/lib/lib/"
      - "/usr/X11R6/lib/lib64/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib/"
      - "/usr/X11R6/lib64/"
      - "/usr/X11R6/"
      - "/usr/pkg/lib/x86_64-linux-gnu/lib/"
      - "/usr/pkg/lib/x86_64-linux-gnu/lib64/"
      - "/usr/pkg/lib/x86_64-linux-gnu/"
      - "/usr/pkg/lib/lib/"
      - "/usr/pkg/lib/lib64/"
      - "/usr/pkg/lib/"
      - "/usr/pkg/lib/"
      - "/usr/pkg/lib64/"
      - "/usr/pkg/"
      - "/opt/lib/x86_64-linux-gnu/lib/"
      - "/opt/lib/x86_64-linux-gnu/lib64/"
      - "/opt/lib/x86_64-linux-gnu/"
      - "/opt/lib/lib/"
      - "/opt/lib/lib64/"
      - "/opt/lib/"
      - "/opt/lib/"
      - "/opt/lib64/"
      - "/opt/"
      - "/usr/lib/X11/lib/"
      - "/usr/lib/X11/lib64/"
      - "/usr/lib/X11/"
    searched_directories:
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/x86_64-linux-gnu/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib64/"
      - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/"
      - "/usr/lib/x86_64-linux-gnu/lib/"
      - "/usr/lib/x86_64-linux-gnu/lib64/"
    found: "/usr/lib/x86_64-linux-gnu/libcrypto.so"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find_package-v1"
    backtrace:
      - "/snap/cmake/1479/share/cmake-4.1/Modules/FindCURL.cmake:170 (find_package)"
      - "CMakeLists.txt:26 (FIND_PACKAGE)"
    name: "CURL"
    configs:
      -
        filename: "CURLConfig.cmake"
        kind: "cmake"
      -
        filename: "curl-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "optional"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "CURL"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects/CURLConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects/curl-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/CURLConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/curl-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake"
      mode: "config"
      version: "7.79.1-DEV"
    search_context:
      CMAKE_PREFIX_PATH:
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG"
        - "/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG"
      ENV{PATH}:
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.local/bin/"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/snap/cmake/1479"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
...
