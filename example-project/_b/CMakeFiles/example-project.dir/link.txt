/usr/bin/c++ -g -Wl,--dependency-file=CMakeFiles/example-project.dir/link.d "CMakeFiles/example-project.dir/main.cpp.o" -o example-projectd  -Wl,-rpath,/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib:/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib: /home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/libcurl-d.so /home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so -ldl /usr/lib/x86_64-linux-gnu/libssl.so /usr/lib/x86_64-linux-gnu/libcrypto.so
