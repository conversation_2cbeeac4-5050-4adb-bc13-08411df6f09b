# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1479/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1479/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/docs/example-project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/docs/example-project/_b

# Include any dependencies generated for this target.
include CMakeFiles/example-project.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/example-project.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/example-project.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/example-project.dir/flags.make

CMakeFiles/example-project.dir/codegen:
.PHONY : CMakeFiles/example-project.dir/codegen

CMakeFiles/example-project.dir/main.cpp.o: CMakeFiles/example-project.dir/flags.make
CMakeFiles/example-project.dir/main.cpp.o: /home/<USER>/work/docs/example-project/main.cpp
CMakeFiles/example-project.dir/main.cpp.o: CMakeFiles/example-project.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/docs/example-project/_b/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/example-project.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/example-project.dir/main.cpp.o -MF CMakeFiles/example-project.dir/main.cpp.o.d -o CMakeFiles/example-project.dir/main.cpp.o -c /home/<USER>/work/docs/example-project/main.cpp

CMakeFiles/example-project.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/example-project.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/docs/example-project/main.cpp > CMakeFiles/example-project.dir/main.cpp.i

CMakeFiles/example-project.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/example-project.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/docs/example-project/main.cpp -o CMakeFiles/example-project.dir/main.cpp.s

# Object files for target example-project
example__project_OBJECTS = \
"CMakeFiles/example-project.dir/main.cpp.o"

# External object files for target example-project
example__project_EXTERNAL_OBJECTS =

example-projectd: CMakeFiles/example-project.dir/main.cpp.o
example-projectd: CMakeFiles/example-project.dir/build.make
example-projectd: CMakeFiles/example-project.dir/compiler_depend.ts
example-projectd: /home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/libcurl-d.so
example-projectd: /home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so
example-projectd: /usr/lib/x86_64-linux-gnu/libssl.so
example-projectd: /usr/lib/x86_64-linux-gnu/libcrypto.so
example-projectd: CMakeFiles/example-project.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/work/docs/example-project/_b/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable example-projectd"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/example-project.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/example-project.dir/build: example-projectd
.PHONY : CMakeFiles/example-project.dir/build

CMakeFiles/example-project.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/example-project.dir/cmake_clean.cmake
.PHONY : CMakeFiles/example-project.dir/clean

CMakeFiles/example-project.dir/depend:
	cd /home/<USER>/work/docs/example-project/_b && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/work/docs/example-project /home/<USER>/work/docs/example-project /home/<USER>/work/docs/example-project/_b /home/<USER>/work/docs/example-project/_b /home/<USER>/work/docs/example-project/_b/CMakeFiles/example-project.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/example-project.dir/depend

