# This is the CMakeCache file.
# For build in directory: /home/<USER>/work/docs/example-project/_b
# It was generated by CMake: /snap/cmake/1479/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Switch on for CMCONF use; off for testing purposes. Do not alter
// this setting unless you know what you are doing.
BA_PACKAGE_CMCONF_USE:BOOL=TRUE

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Build type - initialized from CMDEF_BUILD_TYPE_DEFAULT
CMAKE_BUILD_TYPE:STRING=Debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CMake build types which can be available in multi-configuration
// generators
CMAKE_CONFIGURATION_TYPES:STRING=Debug

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-13

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-13

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/work/docs/example-project/_b/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=example-project

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the archiver during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the archiver during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the archiver during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//If set the configuration is installed as symlink to CMake user
// package registry. If OFF install is skipped.
CMCONF_INSTALL_AS_SYMLINK:BOOL=OFF

//Name of the system for which the configuration is intended.
CMCONF_SYSTEM_NAME:STRING=EXAMPLE

//If set to ON the configuration is uninstalled from CMake user
// package registry. If OFF uninstall is not performed.
CMCONF_UNINSTALL:BOOL=OFF

//Architecture for which we will compile
CMDEF_ARCHITECTURE:STRING=x86-64

//Name of bin directory after install (in package)
CMDEF_BINARY_INSTALL_DIR:PATH=bin

//ON if the CMAKE_BUILD_TYPE is overridden by CMDEF, OFF otherwise
CMDEF_BUILD_TYPE_CMAKE_BUILD_TYPE_OVERRIDE:BOOL=ON

//Default build type if no CMAKE_BUILD_TYPE is specified
CMDEF_BUILD_TYPE_DEFAULT:STRING=Debug

//Allowed CMake configoration types
CMDEF_BUILD_TYPE_LIST:STRING=Debug;Release

//Uppercase list of Build types. The order of types must be the
// same as in CMDEF_BUILD_TYPE_LIST
CMDEF_BUILD_TYPE_LIST_UPPERCASE:STRING=DEBUG;RELEASE

//Distro ID for which we will compile
CMDEF_DISTRO_ID:STRING=ubuntu

//Distro Version ID for which we will compile
CMDEF_DISTRO_VERSION_ID:STRING=24.04

//Name of the company
CMDEF_ENV_DESCRIPTION_COMPANY_NAME:STRING=Company name

//Copyright which will be added to binaries
CMDEF_ENV_DESCRIPTION_COPYRIGHT:STRING=Company name

//Suffix for namespace
CMDEF_ENV_NAMESPACE_SUFFIX:STRING=::

//Separator for package name
CMDEF_ENV_NAME_SEPARATOR:STRING=_

//Suffix for executable name if the target is configured in debug
CMDEF_EXECUTABLE_NAME_DEBUG_SUFFIX:STRING=d

//Name of include directory after install (in package)
CMDEF_INCLUDE_INSTALL_DIR:PATH=include

//List of C++ source file extensions
CMDEF_LANG_CXX_SOURCE_FILE_EXTENSIONS:STRING=.cpp;.cc;.cxx;.hpp

//List of C source file extensions
CMDEF_LANG_C_SOURCE_FILE_EXTENSIONS:STRING=.c;.h

//List of OBJCXX source file extensions
CMDEF_LANG_OBJCXX_SOURCE_FILE_EXTENSIONS:STRING=.mm

//List of OBJC source file extensions
CMDEF_LANG_OBJC_SOURCE_FILE_EXTENSIONS:STRING=.m;.h

//List of RC source file extensions
CMDEF_LANG_RC_SOURCE_FILE_EXTENSIONS:STRING=.rc

//Name of library directory after install (in package)
CMDEF_LIBRARY_INSTALL_DIR:PATH=lib

//Suffix for library name if the target is configured in debug
CMDEF_LIBRARY_NAME_DEBUG_SUFFIX:STRING=d

//Suffix for development library
CMDEF_LIBRARY_NAME_DEV_SUFFIX:STRING=-dev

//Flag which is appended to library name and CMDEF_*_NAME_DEBUG_SUFFIX
CMDEF_LIBRARY_NAME_FLAG_SHARED:STRING=

//Flag which is appended to library name and CMDEF_*_NAME_DEBUG_SUFFIX
CMDEF_LIBRARY_NAME_FLAG_STATIC:STRING=-static

//Suffix for shared library. Determined by host OS.
CMDEF_LIBRARY_NAME_SUFFIX_SHARED:STRING=.so

//.so
CMDEF_LIBRARY_NAME_SUFFIX_SHARED_LINUX:STRING=.so

//.dylib
CMDEF_LIBRARY_NAME_SUFFIX_SHARED_MACOS:STRING=.dylib

//.dll
CMDEF_LIBRARY_NAME_SUFFIX_SHARED_WINDOWS:STRING=.dll

//Suffix for static library. Determined by host OS.
CMDEF_LIBRARY_NAME_SUFFIX_STATIC:STRING=.a

//.a
CMDEF_LIBRARY_NAME_SUFFIX_STATIC_LINUX:STRING=.a

//.a
CMDEF_LIBRARY_NAME_SUFFIX_STATIC_MACOS:STRING=.a

//.lib
CMDEF_LIBRARY_NAME_SUFFIX_STATIC_WINDOWS:STRING=.lib

//Suffix for package names if the installed target is library
CMDEF_LIBRARY_PREFIX:STRING=lib

//Name of folder for CMDEF targets in multiconfig like Visual Studio
CMDEF_MULTICONF_FOLDER_NAME:PATH=CMDEF

//OS version/type
CMDEF_OS_LINUX:BOOL=ON

//OS version/type
CMDEF_OS_MACOS:BOOL=OFF

//String, normalized representation of OS name
CMDEF_OS_NAME:STRING=linux

//Short-string representation of OS name
CMDEF_OS_NAME_SHORT:STRING=lin

//Do we have POSIX OS?
CMDEF_OS_POSIX:BOOL=ON

//OS version/type
CMDEF_OS_WINDOWS:BOOL=OFF

//Name of source directory after install (in package)
CMDEF_SOURCE_INSTALL_DIR:PATH=source

//Supported programming languages
CMDEF_SUPPORTED_LANG_LIST:STRING=C;CXX;OBJC;OBJCXX;RC

//Output directory for CMake targets
CMDEF_TARGET_OUTPUT_DIRECTORY:PATH=/home/<USER>/work/docs/example-project/_b/$<CONFIG>

CMLIB_CACHE_ENTRY_BACPACK_CURL_DEBUG:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/file/BACPACK/CURL/DEBUG/libcurld-dev_v7.79.1_x86-64-ubuntu-24.04.zip

CMLIB_CACHE_ENTRY_BACPACK_ZLIB_DEBUG:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/file/BACPACK/ZLIB/DEBUG/libzlibd-dev_v1.2.11_x86-64-ubuntu-24.04.zip

CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_CMCONF:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMCONF

CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_CMDEF:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMDEF

CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_CMUTIL:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMUTIL

CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_STORAGE:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/STORAGE

CMLIB_CACHE_ENTRY_CMLIB_STORAGE_DEP:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/STORAGE/DEP

CMLIB_CACHE_ENTRY_EXTRACTED_BACPACK_CURL_DEBUG:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG

CMLIB_CACHE_ENTRY_EXTRACTED_BACPACK_ZLIB_DEBUG:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG

CMLIB_CACHE_ENTRY_LIST:STRING=CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_CMDEF;CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_STORAGE;CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_CMUTIL;CMLIB_CACHE_ENTRY_CMLIB_COMPONENT_CMCONF;CMLIB_CACHE_ENTRY_CMLIB_STORAGE_DEP;CMLIB_CACHE_ENTRY_BACPACK_CURL_DEBUG;CMLIB_CACHE_ENTRY_EXTRACTED_BACPACK_CURL_DEBUG;CMLIB_CACHE_ENTRY_BACPACK_ZLIB_DEBUG;CMLIB_CACHE_ENTRY_EXTRACTED_BACPACK_ZLIB_DEBUG

//CMake-lib compatibility version
CMLIB_COMPATIBILITY_VERSION:STRING=1

//If set the path is used to find components. If not set components
// are downloaded from the remote server.
CMLIB_COMPONENT_LOCAL_BASE_PATH:PATH=

//Revision of CMCONF component to use
CMLIB_COMPONENT_REVISION_CMCONF:STRING=v1.1.0

//Revision of CMDEF component to use
CMLIB_COMPONENT_REVISION_CMDEF:STRING=v1.0.0

//Revision of CMUTIL component to use
CMLIB_COMPONENT_REVISION_CMUTIL:STRING=v1.1.0

//Revision of STORAGE component to use
CMLIB_COMPONENT_REVISION_STORAGE:STRING=v1.0.0

//If ON debug messages and checks are enabled, If OFF disable debug
CMLIB_DEBUG:BOOL=OFF

//Enable depenendcy Conrol if ON, Disable dependency conrol if
// OFF
CMLIB_DEPENDENCY_CONTROL:BOOL=ON

//The directory containing a CMake configuration file for CMLIB.
CMLIB_DIR:PATH=/home/<USER>/work/cmakelib

//Git repository default branch
CMLIB_FILE_DOWNLOAD_DEFAULT_REVISION:STRING=master

//If On the git-archive will NOT be used! Conflicted with CMLIB_FILE_DOWNLOAD_GIT_ARCHIVE_ONLY!
CMLIB_FILE_DOWNLOAD_GIT_ARCHIVE_DISABLE:BOOL=OFF

//If On the git-archive will be exclusively used to download files
// from git repository
CMLIB_FILE_DOWNLOAD_GIT_ARCHIVE_ONLY:BOOL=OFF

//HTTP header for File Download. Exactly one headerline can be
// specified! Example: Authorization: Bearer <token>
CMLIB_FILE_DOWNLOAD_HTTP_HEADER:STRING=

//Show download progress if ON. Do not show if OFF.
CMLIB_FILE_DOWNLOAD_SHOW_PROGRESS:BOOL=OFF

//Git executable, v2.43.0
CMLIB_REQUIRED_ENV_GIT_EXECUTABLE:PATH=/usr/bin/git

//Minimum required Git version
CMLIB_REQUIRED_ENV_GIT_MIN_VERSION:STRING=2.17.0

//Name of the Git remote from which the CMLIB_REQUIRED_ENV_REMOTE_URL
// will be obtained
CMLIB_REQUIRED_ENV_REMOTE_NAME:STRING=origin

//Url for remote 'origin'. Cannot be chaned. Dependent on CMLIB_REQUIRED_ENV_REMOTE_NAME
CMLIB_REQUIRED_ENV_REMOTE_URL:STRING=https://github.com/cmakelib

//Cache path where all downloaded files will be stored
CMLIB_REQUIRED_ENV_TMP_PATH:PATH=/home/<USER>/work/docs/example-project/_tmp

//Default URL which is used if no CONFIG_FILENAME is found
CMLIB_STORAGE_REPOSITORY_DEFAULT_URI:STRING=https://github.com/cmakelib/cmakelib-test.git

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL

//CMCONF setting variable
EXAMPLE_BA_PACKAGE_LOCAL_PATH:STRING=/home/<USER>/work/packager/lfsrepo2

//CMCONF setting variable
EXAMPLE_BA_PACKAGE_LOCAL_USE:STRING=ON

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//The directory containing a CMake configuration file for STORAGE.
STORAGE_DIR:PATH=STORAGE_DIR-NOTFOUND

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so

//Value Computed by CMake
example-project_BINARY_DIR:STATIC=/home/<USER>/work/docs/example-project/_b

//Value Computed by CMake
example-project_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
example-project_SOURCE_DIR:STATIC=/home/<USER>/work/docs/example-project

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so


########################
# INTERNAL cache entries
########################

//Package repository revision to use
BA_PACKAGE_VARS__REVISION:INTERNAL=
//Package repository revision to use
BA_PACKAGE_VARS__URI_TEMPLATE:INTERNAL=file:///home/<USER>/work/packager/lfsrepo2/package/<GIT_PATH>/<PACKAGE_GROUP_NAME>/<ARCHIVE_NAME>
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/work/docs/example-project/_b
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/snap/cmake/1479/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/snap/cmake/1479/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/snap/cmake/1479/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/snap/cmake/1479/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/work/docs/example-project
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/snap/cmake/1479/share/cmake-4.1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Set by resource/CMakeLists.txt.in. Do not set manually. COntrols
// whetnever CMake project is allowed to install the configuration.
CMCONF_CMAKE_PROJECT_ALLOW_INSTALL:INTERNAL=OFF
//It is set to ON when CMCONF_GET is called at least once. OFF
// otherwise.
CMCONF_GET_CALLED:INTERNAL=OFF
//Template file for CMakeLists.txt used to install configuration.
CMCONF_INSTALL_CMAKELISTS_TEMPLATE_FILE:INTERNAL=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMCONF/resource/CMakeLists.txt.in
//It is set to ON when installation of configuration is already
// proceeded and succeeded.
CMCONF_INSTALL_PROCEEDED:INTERNAL=OFF
//Prefix for CMake user package name.
CMCONF_PACKAGE_NAME_PREFIX:INTERNAL=CMCONF_
//Directory where CMCONF.cmake is located.
CMCONF_SCRIPT_DIR:INTERNAL=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMCONF
//It is set to ON when CMCONF_SET is called at least once. OFF
// otherwise.
CMCONF_SET_CALLED:INTERNAL=OFF
//String representation of OS name - upper case
CMDEF_OS_NAME_UPPER:INTERNAL=LINUX
//Delimiter for items in control template. Look at CMLIB_CACHE_CONTROL_TEMPLATE.
CMLIB_CACHE_CONTROL_ITEMS_DELIM:INTERNAL=,
//Delimiter for keywords in control file. Do NOT use ';'
CMLIB_CACHE_CONTROL_KEYWORDS_KEYDELIM:INTERNAL=|
//Base directory for meta information for cache control
CMLIB_CACHE_CONTROL_META_BASE_DIR:INTERNAL=/home/<USER>/work/docs/example-project/_tmp/cache_control
//Base directory for control files of cache control
CMLIB_CACHE_CONTROL_META_CONTROL_DIR:INTERNAL=/home/<USER>/work/docs/example-project/_tmp/keys_control
//Template for cache control file.AS delimiter the ',' must be
// used
CMLIB_CACHE_CONTROL_TEMPLATE:INTERNAL=<KEYWORDS_STRING>,<URI>,<GIT_PATH>,<GIT_REVISION>
//Inactivity timeout for File Download
CMLIB_FILE_DOWNLOAD_TIMEOUT:INTERNAL=100
//ADVANCED property for variable: CURL_DIR
CURL_DIR-ADVANCED:INTERNAL=1
//Details about finding CURL
FIND_PACKAGE_MESSAGE_DETAILS_CURL:INTERNAL=[/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/CURL/DEBUG/lib/cmake/CURL/CURLConfig.cmake][ ][v7.79.1-DEV()]
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[/usr/bin/git][v2.43.0()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/lib/libz.so][/home/<USER>/work/docs/example-project/_tmp/cache/dir/EXTRACTED/BACPACK/ZLIB/DEBUG/include][ ][v1.2.11()]
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
_CMDEF_PACKAGE_CURRENT_DIR:INTERNAL=/home/<USER>/work/docs/example-project/_tmp/cache/dir/CMLIB/COMPONENT/CMDEF/system_modules
//Name of the directory in ehich the cache in the TMP will be located
_CMLIB_CACHE_VAR_DIRECTORY_NAME:INTERNAL=cache
//Var entry cache description
_CMLIB_CACHE_VAR_ENTRY_LIST_DESC:INTERNAL=List of all cache entries
//Cache entry var prefix
_CMLIB_CACHE_VAR_ENTRY_PREFIX:INTERNAL=CMLIB_CACHE_ENTRY
//List of available components.
_CMLIB_COMPONENT_AVAILABLE_LIST:INTERNAL=cmdef;storage;cmutil;cmconf
//Filename prefix for components.
_CMLIB_COMPONENT_REPO_NAME_PREFIX:INTERNAL=cmakelib-component-
//Prefix for component revision variable name.
_CMLIB_COMPONENT_REVISION_VARANAME_PREFIX:INTERNAL=CMLIB_COMPONENT_REVISION_
//Filename if the STORAGE config located in GIT root
_CMLIB_STORAGE_CONFIG_FILENAME:INTERNAL=CMLibStorage.cmake
_OPENSSL_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-L/usr/lib/x86_64-linux-gnu;-ldl;-pthread;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;dl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.0.13
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

